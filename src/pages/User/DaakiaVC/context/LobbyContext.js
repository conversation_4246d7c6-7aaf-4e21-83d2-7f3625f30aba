import React, { createContext, useContext, useState } from 'react';

// Create the context
const LobbyContext = createContext();

// Custom hook to use the lobby context
export const useLobby = () => {
  const context = useContext(LobbyContext);
  if (!context) {
    throw new Error('useLobby must be used within a LobbyProvider');
  }
  return context;
};

// Provider component
export const LobbyProvider = ({ children }) => {
  const [lobbyParticipants, setLobbyParticipants] = useState(new Map());

  const value = {
    lobbyParticipants,
    setLobbyParticipants,
  };

  return (
    <LobbyContext.Provider value={value}>
      {children}
    </LobbyContext.Provider>
  );
};

export default LobbyContext;
