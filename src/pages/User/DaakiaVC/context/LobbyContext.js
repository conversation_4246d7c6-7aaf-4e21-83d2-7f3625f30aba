import { createContext, useContext, useState, useMemo, useEffect } from 'react';
import { parseMetadata } from '../utils/helper';
import AllowParticipant from '../components/AllowParticipant';
import lobbyNotification from '../assets/sounds/lobby-notification.mp3';

// Create the context
const LobbyContext = createContext();

// Custom hook to use the lobby context
export const useLobby = () => {
  const context = useContext(LobbyContext);
  if (!context) {
    throw new Error('useLobby must be used within a LobbyProvider');
  }
  return context;
};

// Provider component
export function LobbyProvider({ children }) {
  const [lobbyParticipants, setLobbyParticipants] = useState(new Map());

  // Cleanup interval useEffect - removes old lobby participants
  useEffect(() => {
    const interval = setInterval(() => {
      const currentTime = Date.now();
      const timeLimit = 12 * 1000; // 12 seconds in milliseconds

      setLobbyParticipants((prev) => {
        const updatedMap = new Map(prev);
        Array.from(updatedMap.keys()).forEach((key) => {
          const participant = updatedMap.get(key);
          if (participant) {
            const timeDifference = currentTime - participant.time;
            if (timeDifference > timeLimit) {
              updatedMap.delete(key);
            }
          }
        });
        return updatedMap;
      });
    }, 2000); // Check every 2 seconds

    return () => clearInterval(interval);
  }, []);

  // Function to handle lobby data received
  const handleLobbyDataReceived = (data, room, isHost, id, coHostToken, setToastNotification, setToastStatus, setShowToast) => {
    if (
      isHost ||
      parseMetadata(room.localParticipant.metadata)?.role_name === "cohost"
    ) {
      setLobbyParticipants((prev) => {
        const updatedMap = new Map(prev);
        const currentTime = Date.now();

        if (updatedMap.has(data.request_id)) {
          const existingData = updatedMap.get(data.request_id);
          updatedMap.set(data.request_id, {
            ...existingData,
            time: currentTime,
          });
        } else {
          updatedMap.set(data.request_id, { ...data, time: currentTime });
          setToastStatus("content");
          setToastNotification(
            <AllowParticipant
              participantName={data.display_name}
              requestId={data.request_id}
              id={id}
              setShowToast={setShowToast}
              coHostToken={coHostToken}
              localParticipant={room?.localParticipant}
            />
          );
          setShowToast(true);
          new Audio(lobbyNotification).play();
        }

        return updatedMap;
      });
    }
  };

  const value = useMemo(() => ({
    lobbyParticipants,
    setLobbyParticipants,
    handleLobbyDataReceived,
  }), [lobbyParticipants]);

  return (
    <LobbyContext.Provider value={value}>
      {children}
    </LobbyContext.Provider>
  );
}

export default LobbyContext;
